============================================================
PAGE 51
============================================================

# Files
## Overview
 The Niagara Framework is built upon the fundamental principle that everything of interest is modeled as a BObject.
 Files are one of the most basic entities which are mapped into the object model.
 The Niagara file model is a comprehensive architecture for mapping all files into a consistent set of APIs:
Files on the local file system (java.io.File);
Files stored within modules and zip files;
Files over the network using Fox;
Files over the network using HTTP;
Files over the network using FTP;
Files in memory;
Files which are autogenerated;
## API
 The javax.baja.file package provides the core APIs used for file acess. There are three core concepts in the file
 model:
1. BIFile: represents a file. In general file extensions are mapped to specific Types of BIFile using the registry.
 Effectively the Niagara Type wraps the MIME type. For example common file types include file:TextFile,
 file:XmlFile, file:ImageFile, file:WordFile. The "file" module contains mappings for common file
 extensions.
2. BIFileStore: models a BIFile backing store. For example a file:TextFile might exist on the file system,
 in a zip file, or over a network. Each of these file storage mechanism reads and writes the file differently. There a
 BIFileStore for every BIFile which may be accessed via the BIFile.getStore() method. Common store
 types include baja:LocalFileStore, baja:MemoryFileStore, and baja:ZipFileEntry.
3. BFileSpace: represents a set of files with a common storage model. BFileSpaces are responsible for resolving
 FilePaths into BIFiles. The prototypical file space is the singleton for local file system BFileSystem. The
 ord "local:|file:" always maps to BFileSystem.INSTANCE.
## Mapping File Extensions
 You can create custom file types for specific file extensions by following these rules:
Create an implementation of BIFile. Utilize one of the existing base classes such as baja:DataFile. If you
 wish to utilize agents such as file text editors then you must extent file:TextFile or at least implement
 file:ITextFile.
Make sure you override getMimeType() to return the MIME type for the file's contents:

public String getMimeType() { return "text/html"; }

Provide a custom icon if you wish by overriding the getIcon() method:

public BIcon getIcon() { return icon; }
private static final BIcon icon = BIcon.std("files/html.png");

Map one of more file extensions to your type using in "module-include.xml":
============================================================
PAGE 52
============================================================

  <type name="HtmlFile" class="javax.baja.file.types.text.BHtmlFile">
    <file>
      <ext name="html"/>
      <ext name="htm"/>
    </file>
  </type>
