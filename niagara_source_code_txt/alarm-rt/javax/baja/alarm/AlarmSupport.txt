/**
 * Copyright 2003 Tridium, Inc. All Rights Reserved.
 */
package javax.baja.alarm;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.baja.alarm.ext.BAlarmSourceExt;
import javax.baja.naming.BOrdList;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BObject;
import javax.baja.sys.Clock;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.util.BUuid;
import javax.baja.util.Queue;

import com.tridium.sys.schema.Fw;

/**
 * AlarmSupport is a support class to enabled easy alarm generation and alarm
 * handling for BIAlarmSources.
 * <p>
 * The BIAlarmSource should have a single instance of AlarmSupport.
 * newOffnormalAlarm() is used to generate new alarms. toNormal() should be called
 * when the source goes back to its normal condition. And ackAlarm should be
 * called from the doAckAlarm() method.
 *
 * <AUTHOR>
 */
public class AlarmSupport
{
  public AlarmSupport(BIAlarmSource comp, String alarmClassName)
  {
    this.comp = (BComponent)comp;
    this.alarmClassName = alarmClassName;
  }

  public AlarmSupport(BIAlarmSource comp, BAlarmSourceInfo info)
  {
    this.comp = (BComponent)comp;
    alarmClassName = info.getAlarmClass();
    this.info = info;
  }

  /**
   * Set the AlarmClass for alarms generated by this object.
   */
  public void setAlarmClass(String alarmClassName)
  {
    this.alarmClassName = alarmClassName;
    // getAlarmService() also updates alarmClass
    getAlarmService();
  }

  /**
   * @return the ord for the source used in the AlarmRecord.
   */
  public BOrdList getSourceOrd()
  {
    return BOrdList.make(comp.getNavOrd());
  }

  public String getAlarmClassName()
  {
    return info != null ? info.getAlarmClass() : alarmClassName;
  }

  /**
   * @return true if the AlarmClass requires an ack for the given transition.
   */
  public boolean isAckRequired(BSourceState trans)
  {
    // ensure alarmClass is up-to-date with alarmClassName
    getAlarmService();
    return alarmClass != null && alarmClass.getAckRequired().includes(trans);
  }

  /**
   * Generate an alert using alarmData from BAlarmSourceInfo.
   */
  public BAlarmRecord newAlert() throws Exception
  {
    return newAlert(BFacets.DEFAULT);
  }

  /**
   * An Alert is an alarm that has no toNormal transition.
   */
  public BAlarmRecord newAlert(BFacets alarmData) throws Exception
  {
    return newAlarm(BSourceState.alert, alarmData);
  }

  /**
   * Generate an offnormal alarm using alarmData from BAlarmSourceInfo..
   */
  public BAlarmRecord newOffnormalAlarm() throws Exception
  {
    return newOffnormalAlarm(BFacets.DEFAULT);
  }

  /**
   * Generate and route a new offnormal alarm.
   */
  public BAlarmRecord newOffnormalAlarm(BFacets alarmData) throws Exception
  {
    return newAlarm(BSourceState.offnormal, alarmData);
  }

  /**
   * Generate a fault alarm using alarmData from BAlarmSourceInfo.
   */
  public BAlarmRecord newFaultAlarm() throws Exception
  {
    return newFaultAlarm(BFacets.DEFAULT);
  }

  /**
   * Generate and route a new fault alarm.
   */
  public BAlarmRecord newFaultAlarm(BFacets alarmData) throws Exception
  {
    return newAlarm(BSourceState.fault, alarmData);
  }

  /**
   * Generate a new Alarm based on the AlarmState.

   * @return BAlarmRecord that was sent to AlarmService.
   */
  protected BAlarmRecord newAlarm(BSourceState state, BFacets alarmData)
  {
    lastTransition = state;

    boolean ackRequired = isAckRequired(state);

    BAlarmRecord alarm = new BAlarmRecord(BUuid.make());

    // Issue 14411 - Alert record type in activity monitor says "Unknown User" in authority column
    // If USER_OVERRIDE_FACET is present in the facets, remove it from the facets and override the
    // User on the BAlarmRecord
    String userOverride = alarmData.gets(USER_OVERRIDE_FACET, null);
    if (userOverride != null)
    {
      alarm.setUser(userOverride);
      alarmData = BFacets.makeRemove(alarmData, USER_OVERRIDE_FACET);
    }

    if (info != null)
    {
      alarmData = BFacets.make(info.makeAlarmData(state), alarmData);
    }

    alarm.setSource(getSourceOrd());
    alarm.setAlarmClass(getAlarmClassName());
    alarm.setAlarmTransition(state);
    alarm.setSourceState(state);
    alarm.setAckRequired(ackRequired);
    alarm.setAlarmData(alarmData);

    if (state.equals(BSourceState.offnormal))
    {
      lastOffnormal = alarm;
    }
    else if (state.equals(BSourceState.fault))
    {
      lastFault = alarm;
    }
    else if (state.equals(BSourceState.alert))
    {
      lastAlert = alarm;
    }

    alarmCount++;

    if (getAlarmService()!= null)
    {
      getAlarmService().routeAlarm(alarm);
      return alarm;
    }
    else
    {
      logger.severe("Unable to route new alert for " + getSourceOrd());
      return null;
    }
  }

  /**
   * Generate an toNormal notification using alarmData from BAlarmSourceInfo.
   * This method makes use of the BAlarmDatabase.toNormal method when needed,
   * allowing for potential optimization.
   * <p>
   * This method generates the alarm transition asynchronously to the current thread.
   *
   * @param cx unused
   * @since Niagara 3.6
   */
  public synchronized void toNormal(Context cx)
    throws Exception
  {
    toNormal(BFacets.DEFAULT, cx);
  }

  /**
   * Generate and route toNormal notifications for all current offnormal and
   * fault alarms. This method makes use of the BAlarmDatabase.toNormal method
   * when needed, allowing for potential optimization.
   * <p>
   * This method generates the alarm transition asynchronously to the current thread.
   *
   * @param newFacets to add the BAlarmRecord
   * @param ignored unused
   * @since Niagara 3.6
   */
  public synchronized void toNormal(BFacets newFacets, Context ignored)
    throws Exception
  {
    lastTransition = BSourceState.normal;

    if (info != null)
    {
      newFacets = BFacets.make(newFacets, info.makeAlarmData(BSourceState.normal));
    }

    if (getAlarmService() == null)
    {
      logger.severe("Unable to transition alarm to Normal for " + getSourceOrd());
      return;
    }

    lastNormal = null;
    BAbsTime toNormalTimestamp = BAbsTime.now();
    if (lastAlert != null && !lastAlert.isNormal())
    {
      lastAlert.setSourceState(BSourceState.normal);
      lastAlert.setNormalTime(toNormalTimestamp);
      lastNormal = lastAlert;
    }

    if (lastFault != null && !lastFault.isNormal())
    {
      lastFault.setSourceState(BSourceState.normal);
      lastFault.setNormalTime(toNormalTimestamp);
      if (lastNormal == null || lastFault.getTimestamp().isAfter(lastNormal.getTimestamp()))
      {
        lastNormal = lastFault;
      }
    }

    if (lastOffnormal != null && !lastOffnormal.isNormal())
    {
      lastOffnormal.setSourceState(BSourceState.normal);
      lastOffnormal.setNormalTime(toNormalTimestamp);
      if (lastNormal == null || lastOffnormal.getTimestamp().isAfter(lastNormal.getTimestamp()))
      {
        lastNormal = lastOffnormal;
      }
    }

    boolean all = alarmCount > 1 || firstNormal;
    Queue q = (Queue)Sys.getService(BAlarmService.TYPE).fw(Fw.GET_ALARM_QUEUE, null, null, null, null);
    q.enqueue(new ToNormalTransition(lastNormal, alarmClass, newFacets, all, toNormalTimestamp));

    firstNormal = false;
    alarmCount = 0;
  }

  /**
   * @return true if alarm acked (clear ack bit in status), false if stale ack
   */
  public boolean ackAlarm(BAlarmRecord alarm) throws Exception
  {
    alarm.setAckTime(Clock.time());
    alarm.setAckState(BAckState.acked);
    alarm.setAckRequired(false);
    alarm.setAlarmClass(getAlarmClassName());

    if (lastTransition == BSourceState.normal && alarm.getSourceState() != BSourceState.normal)
    {
      alarm.setSourceState(BSourceState.normal);
    }

    // Check if this record is for the most recent alarm of that type using the uuid
    boolean validAck = false;
    if (alarm.getAlarmTransition() == BSourceState.offnormal)
    {
      validAck = lastOffnormal == null || alarm.getUuid().equals(lastOffnormal.getUuid());
    }
    else if (alarm.getAlarmTransition() == BSourceState.fault)
    {
      validAck = lastFault == null || alarm.getUuid().equals(lastFault.getUuid());
    }
    else if (alarm.getAlarmTransition() == BSourceState.alert)
    {
      validAck = lastAlert == null || alarm.getUuid().equals(lastAlert.getUuid());
    }

    if (getAlarmService() == null)
    {
      logger.severe("Cannot route alarm for " + getSourceOrd());
    }
    else
    {
      getAlarmService().routeAlarm(alarm);
    }

    return validAck;
  }

  public boolean isValidNormalAck(BAlarmRecord ackRequest)
  {
    return lastNormal == null || ackRequest.getUuid().equals(lastNormal.getUuid());
  }

  /**
   * Return the most recently generated normal alarm,
   * null if no normal alarms have been generated since station start.
   *
   * @since Niagara 4.10u5
   * @since Niagara 4.12u2
   * @since Niagara 4.13
   */
  public BAlarmRecord getLastNormal()
  {
    return lastNormal;
  }

  /**
   * Return the most recently generated offnormal alarm,
   * null if no offnormal alarms have been generated since station start.
   */
  public BAlarmRecord getLastOffnormal()
  {
    return lastOffnormal;
  }

  /**
   * Return the most recently generated fault alarm,
   * null if no fault alarms have been generated since station start.
   */
  public BAlarmRecord getLastFault()
  {
    return lastFault;
  }

  /**
   * Return the most recently generated alert,
   * null if no alert have been generated since station start.
   */
  public BAlarmRecord getLastAlert()
  {
    return lastAlert;
  }

  private BAlarmService getAlarmService()
  {
    if (as == null)
    {
      try
      {
        as = (BAlarmService)Sys.getService(BAlarmService.TYPE);
        alarmClass = as.lookupAlarmClass(getAlarmClassName());
      }
      catch (NullPointerException npe)
      {
        logger.log(Level.SEVERE, "AlarmSupport could not resolve AlarmService");
        return null;
      }
    }
    return as;
  }

  private static final Logger logger = Logger.getLogger("alarm");

  private final BComponent comp;
  private String alarmClassName;
  private BAlarmClass alarmClass;
  private BAlarmSourceInfo info;

  private BSourceState lastTransition;

  private BAlarmRecord lastOffnormal;
  private BAlarmRecord lastFault;
  private BAlarmRecord lastAlert;
  private BAlarmRecord lastNormal;

  // # of alarms generated but not set to normal
  private int alarmCount;
  // used to force a bql query the first time the point goes to normal
  private boolean firstNormal = true;

  BAlarmService as;
  public static final String USER_OVERRIDE_FACET = "fw_BAlarmRecord_user";

  /**
   * Runnable for transitioning alarm sources toNormal.
   * This allows the potential DB lookup to be run off of the Nre:Engine thread.
   */
  private class ToNormalTransition implements Runnable
  {
    private final BAlarmRecord lastRecord;
    private final BAlarmClass alarmClass;
    private final boolean all;
    private final BFacets newFacets;
    private final BAbsTime toNormalTimestamp;

    ToNormalTransition(BAlarmRecord lastRecord, BAlarmClass alarmClass, BFacets newFacets, boolean all, BAbsTime toNormalTimestamp)
    {
      this.lastRecord = lastRecord;
      this.alarmClass = alarmClass;
      this.all = all;
      this.newFacets = newFacets;
      this.toNormalTimestamp = toNormalTimestamp;
    }

    @Override
    public void run()
    {
      BAlarmService alarmService = getAlarmService();
      if (alarmService == null)
      {
        logger.log(Level.SEVERE, "Error transitioning alarm to normal (" + getSourceOrd() + ')');
        return;
      }

      boolean ackRequired = alarmClass.getAckRequired().isToNormal();

      if (all)
      {
        BAlarmRecord record = new BAlarmRecord(BUuid.make());
        if (ackRequired)
        {
          record.setAckState(BAckState.unacked);
        }
        record.setSource(getSourceOrd());
        record.setAlarmClass(getAlarmClassName());
        record.setNormalTime(toNormalTimestamp);
        record.setSourceState(BSourceState.normal);
        record.setAckRequired(ackRequired);
        record.setAlarmData(newFacets);

        // Since this is potentially a long-running operation, we'll let the database execute it
        // in a database optimized way.
        alarmService.getAlarmDb().toNormal(record);
      }
      else
      {
        BAlarmRecord record;
        if (lastRecord != null)
        {
          // We have a record of the last alarm so make a call to alarmDb.getRecord(), which is much
          // faster than the above bql query.
          try (AlarmDbConnection conn = alarmService.getAlarmDb().getDbConnection(null))
          {
            lastRecord.setSourceState(BSourceState.normal);

            record = conn.getRecord(lastRecord.getUuid());

            // If the alarmDb does not contain our record, then it has rolled off, so we must
            // recreate it.
            if (record == null)
            {
              record = new BAlarmRecord(BUuid.DEFAULT);
              record.setUuid(lastRecord.getUuid());
            }
          }
          catch (IOException ioe)
          {
            logger.log(Level.SEVERE, "Unable to complete toNormal transition", ioe);
            return;
          }
        }
        else
        {
          record = new BAlarmRecord(BUuid.make());
        }

        if (ackRequired)
        {
          record.setAckState(BAckState.unacked);
        }
        record.setSource(getSourceOrd());
        record.setAlarmClass(getAlarmClassName());
        record.setNormalTime(toNormalTimestamp);
        record.setSourceState(BSourceState.normal);
        record.setAckRequired(record.getAckRequired() || ackRequired);
        record.setAlarmData(BFacets.make(record.getAlarmData(), newFacets));

        // The following 3 lines are BAlarmService.doRouteToRecipient()
        BAlarmClass ac = alarmService.lookupAlarmClass(record.getAlarmClass());
        // use doRouteAlarm so this is NOT async
        ac.doRouteAlarm(record);
        alarmService.fireAlarm(record);

        BOrdList list = getSourceOrd();
        BObject obj = list.get(0).resolve().get();
        if (obj instanceof BAlarmSourceExt)
        {
          BAlarmSourceExt ext = (BAlarmSourceExt) obj;
          ext.fireToNormal(record);
        }
      }
    }
  }
}
