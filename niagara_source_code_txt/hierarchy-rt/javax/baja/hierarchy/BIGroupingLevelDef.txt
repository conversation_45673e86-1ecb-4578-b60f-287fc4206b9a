/*
  Copyright 2014 Tridium, Inc. All Rights Reserved.
 */
package javax.baja.hierarchy;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BInterface;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * A BIGroupingLevelDef is a marker interface for any BLevelDef that is used to
 *  create groups of entities.
 *
 * <AUTHOR>
 * @creation  4 Mar 2014
 * @since     Niagara 4.0
 */
@NiagaraType
public interface BIGroupingLevelDef
  extends BInterface
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $javax.baja.hierarchy.BIGroupingLevelDef(2979906276)1.0$ @*/
/* Generated Tue Jan 18 11:31:27 CST 2022 by Slot-o-<PERSON><PERSON> (c) Tridium, Inc. 2012-2022 */

  //region Type

  Type TYPE = Sys.loadType(BIGroupingLevelDef.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/
}
