/*
 * Copyright 2002 Tridium, Inc. All Rights Reserved.
 */
package javax.baja.naming;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.*;

/**
 * <AUTHOR>
 * @creation  2 March 2010
 * @version   $Revision: 1$ $Date: 10/1/10 10:02:17 AM EDT$
 * @since     Baja 1.0
 */
@NiagaraType
public abstract class BStationSessionScheme
  extends BOrdScheme
{
//region /*+ ------------ B<PERSON>IN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $javax.baja.naming.BStationSessionScheme(2979906276)1.0$ @*/
/* Generated Wed Dec 29 19:27:38 CST 2021 by Slot-o-<PERSON><PERSON> (c) Tridium, Inc. 2012-2021 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStationSessionScheme.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Methods
////////////////////////////////////////////////////////////////  

  protected BStationSessionScheme(String schemeId)
  {
    super(schemeId);
  }

  public abstract int getDefaultPort();
}
