/*
 * Copyright 2011, Tridium, Inc. All Rights Reserved.
 */
package javax.baja.web.app;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BInterface;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Interface for a Web App
 * <p>
 * This interface should only be implemented on classes that extend BApp
 *
 * <AUTHOR>
 * @creation 	28 Jul 2011
 * @version 	1
 * @since 		Niagara 3.7
 */
@NiagaraType
public interface BIWebApp
    extends BInterface
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $javax.baja.web.app.BIWebApp(2979906276)1.0$ @*/
/* Generated Fri Jan 14 13:34:19 CST 2022 by <PERSON>lot-o-<PERSON><PERSON> (c) Tridium, Inc. 2012-2022 */

  //region Type

  Type TYPE = Sys.loadType(BIWebApp.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/
}
