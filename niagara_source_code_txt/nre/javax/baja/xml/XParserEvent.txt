/*
 * Copyright 2017, Tridium, Inc. All Rights Reserved.
 */
package javax.baja.xml;

import java.util.List;

/**
 * An event generated by XParserEventGenerator.
 *
 * <AUTHOR>
 * @since Niagara 4.6
 */
public interface XParserEvent
{

  /**
   * Gets the content. This is either an XElem for ELEM_START / ELEM_END events,
   * or XText for TEXT events.
   *
   * @return the content
   */
  XContent getContent();

  /**
   * Gets the location of this event, which is the opening / closing element
   * in the case ELEM_START / ELEM_END events, or the parent element, in
   * the case of a TEXT event.
   *
   * @return
   */
  XElemLocation getLocation();

  /**
   * Gets the event id. The event id is one of: XParser.EOF, XParser.ELEM_START.
   * XParser.ELEM_END, XParser.TEXT.
   *
   * @return the event id
   */
  int getEventId();

  /**
   * If the current element is self closing (i.e. ends with {@code "/>"}), and the event
   * is either ELEM_START / ELEM_END, then this method returns true
   *
   * @return true, if is self closing
   */
  boolean isSelfClosing();
}
